"""
Message Listener for WhatsApp AI Assistant.
Handles incoming WhatsApp messages and processes them for AI response generation.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
import re

from utils.logging import get_whatsapp_logger
from utils.config import get_config
from data.database import get_database_manager
from data.models import Contact, Message, Conversation

logger = get_whatsapp_logger()


class MessageListener:
    """Listens for and processes incoming WhatsApp messages."""
    
    def __init__(self, whatsapp_client):
        self.whatsapp_client = whatsapp_client
        self.config = get_config()
        self.db_manager = get_database_manager()
        
        # Message processing settings
        self.ignore_own_messages = True
        self.ignore_groups = self.config.whatsapp.message_processing.get('ignore_groups', False)
        self.ignore_broadcasts = True
        self.process_media = False
        
        # Setup event handlers
        self.setup_handlers()
        
        logger.info("Message listener initialized")
    
    def setup_handlers(self):
        """Setup event handlers for WhatsApp events."""
        self.whatsapp_client.add_event_handler('message', self.handle_message)
        self.whatsapp_client.add_event_handler('message_ack', self.handle_message_ack)
        self.whatsapp_client.add_event_handler('group_join', self.handle_group_join)
        self.whatsapp_client.add_event_handler('group_leave', self.handle_group_leave)
        
        logger.debug("Event handlers registered")
    
    async def handle_message(self, message_data: Dict[str, Any]):
        """Handle incoming WhatsApp messages."""
        try:
            # Extract message information
            message_id = message_data.get('id')
            body = message_data.get('body', '')
            message_type = message_data.get('type', 'text')
            timestamp = message_data.get('timestamp')
            from_id = message_data.get('from')
            to_id = message_data.get('to')
            from_me = message_data.get('fromMe', False)
            has_media = message_data.get('hasMedia', False)
            is_forwarded = message_data.get('isForwarded', False)
            is_status = message_data.get('isStatus', False)
            
            # Contact and chat information
            contact_data = message_data.get('contact', {})
            chat_data = message_data.get('chat', {})
            
            # Log received message with high visibility
            sender_name = contact_data.get('name') or contact_data.get('pushname') or 'Unknown'
            chat_name = chat_data.get('name', 'Unknown Chat')
            is_group = chat_data.get('isGroup', False)
            
            # Print to console for immediate visibility
            print("\n" + "="*60)
            if from_me:
                print(f"📤 MESSAGE SENT TO: {chat_name}")
                logger.info(f">>> MESSAGE SENT >>> To: {chat_name} | Content: {body[:50]}{'...' if len(body) > 50 else ''}")
            else:
                print(f"📥 MESSAGE RECEIVED FROM: {sender_name} in {chat_name}")
                logger.info(f"<<< MESSAGE RECEIVED <<< From: {sender_name} in {chat_name} | Content: {body[:50]}{'...' if len(body) > 50 else ''}")
            print(f"Content: {body}")
            print("="*60 + "\n")
            
            # Log additional details at debug level
            logger.debug(f"Processing message {message_id} from {from_id}")
            logger.debug(f"Message details: type={message_type}, forwarded={is_forwarded}, has_media={has_media}")
            
            # Apply filters
            if not self.should_process_message(message_data):
                logger.debug(f"Message {message_id} filtered out")
                return
            
            # Process contact
            contact = await self.process_contact(contact_data)
            if not contact:
                logger.warning(f"Could not process contact for message {message_id}")
                return
            
            # Process conversation
            conversation = await self.process_conversation(chat_data, contact.id)
            
            # Store message in database
            stored_message = await self.store_message(
                message_data, contact.id, conversation.id if conversation else None
            )
            
            if stored_message:
                logger.info(f"Message {message_id} stored successfully")
                
                # Trigger AI processing if this is an incoming message
                if not from_me and not is_status:
                    await self.trigger_ai_processing(stored_message, contact.id, conversation)
            
        except Exception as e:
            logger.error(f"Error handling message: {e}")
    
    def should_process_message(self, message_data: Dict[str, Any]) -> bool:
        """Determine if a message should be processed."""
        # Check if it's our own message
        if self.ignore_own_messages and message_data.get('fromMe', False):
            return False
        
        # Check if it's a status update
        if message_data.get('isStatus', False):
            return False
        
        # Check if it's a broadcast
        if self.ignore_broadcasts and message_data.get('broadcast', False):
            return False
        
        # Check if it's a group message
        chat_data = message_data.get('chat', {})
        if self.ignore_groups and chat_data.get('isGroup', False):
            return False
        
        # Check if it has media and we're not processing media
        if not self.process_media and message_data.get('hasMedia', False):
            return False
        
        return True
    
    async def process_contact(self, contact_data: Dict[str, Any]) -> Optional[Contact]:
        """Process and store contact information."""
        try:
            contact_id = contact_data.get('id', '')
            name = contact_data.get('name') or contact_data.get('pushname')
            number = contact_data.get('number', '')
            is_my_contact = contact_data.get('isMyContact', False)
            is_group = contact_data.get('isGroup', False)
            
            # Extract phone number from contact ID if not provided
            if not number and contact_id:
                # WhatsApp contact IDs are usually in format: number@c.<NAME_EMAIL>
                number_match = re.match(r'(\d+)@', contact_id)
                if number_match:
                    number = number_match.group(1)
            
            if not number:
                logger.warning(f"No phone number found for contact {contact_id}")
                return None
            
            # Check if contact exists
            with self.db_manager.get_session() as session:
                contact = session.query(Contact).filter_by(phone_number=number).first()

                if contact:
                    # Update existing contact
                    if name and name != contact.name:
                        contact.name = name
                    contact.last_seen = datetime.now()
                    contact.updated_at = datetime.now()
                else:
                    # Create new contact
                    contact = Contact(
                        phone_number=number,
                        name=name,
                        category="other",
                        consent_given=False,  # Default to no consent
                        privacy_level="private",
                        last_seen=datetime.now()
                    )
                    session.add(contact)

                session.commit()
                session.refresh(contact)

                # Expunge the contact from the session to avoid binding issues
                session.expunge(contact)

                logger.debug(f"Contact processed: {contact.phone_number}")
                return contact
                
        except Exception as e:
            logger.error(f"Error processing contact: {e}")
            return None
    
    async def process_conversation(self, chat_data: Dict[str, Any], contact_id: int) -> Optional[Conversation]:
        """Process and store conversation information."""
        try:
            chat_id = chat_data.get('id', '')
            chat_name = chat_data.get('name', '')
            is_group = chat_data.get('isGroup', False)
            unread_count = chat_data.get('unreadCount', 0)
            timestamp = chat_data.get('timestamp')
            
            if not chat_id:
                return None
            
            with self.db_manager.get_session() as session:
                conversation = session.query(Conversation).filter_by(
                    contact_id=contact_id,
                    chat_id=chat_id
                ).first()
                
                if conversation:
                    # Update existing conversation
                    conversation.last_message_timestamp = datetime.fromtimestamp(timestamp) if timestamp else datetime.now()
                    conversation.message_count += 1
                    conversation.needs_response = not is_group  # Groups don't automatically need responses
                    conversation.updated_at = datetime.now()
                else:
                    # Create new conversation
                    conversation = Conversation(
                        contact_id=contact_id,
                        chat_id=chat_id,
                        last_message_timestamp=datetime.fromtimestamp(timestamp) if timestamp else datetime.now(),
                        message_count=1,
                        is_active=True,
                        needs_response=not is_group,
                        priority_level=1
                    )
                    session.add(conversation)
                
                session.commit()
                session.refresh(conversation)

                # Expunge the conversation from the session to avoid binding issues
                session.expunge(conversation)

                logger.debug(f"Conversation processed: {conversation.chat_id}")
                return conversation
                
        except Exception as e:
            logger.error(f"Error processing conversation: {e}")
            return None
    
    async def store_message(self, message_data: Dict[str, Any], contact_id: int, conversation_id: Optional[int]) -> Optional[Message]:
        """Store message in the database."""
        try:
            message_id = message_data.get('id')
            body = message_data.get('body', '')
            message_type = message_data.get('type', 'text')
            timestamp = message_data.get('timestamp')
            from_me = message_data.get('fromMe', False)
            has_media = message_data.get('hasMedia', False)
            is_forwarded = message_data.get('isForwarded', False)
            
            # Contact and chat information
            contact_data = message_data.get('contact', {})
            chat_data = message_data.get('chat', {})
            
            sender_name = contact_data.get('name') or contact_data.get('pushname') or 'Unknown'
            chat_id = chat_data.get('id', '')
            
            with self.db_manager.get_session() as session:
                # Check if message already exists
                existing_message = session.query(Message).filter_by(
                    chat_id=chat_id,
                    timestamp=datetime.fromtimestamp(timestamp) if timestamp else datetime.now()
                ).first()
                
                if existing_message:
                    logger.debug(f"Message already exists: {message_id}")
                    # Expunge to avoid session binding issues
                    session.expunge(existing_message)
                    return existing_message
                
                # Create new message
                message = Message(
                    chat_id=chat_id,
                    contact_id=contact_id,
                    message_text=body,
                    message_type=message_type,
                    sender_name=sender_name,
                    is_from_me=from_me,
                    timestamp=datetime.fromtimestamp(timestamp) if timestamp else datetime.now(),
                    processed=False,
                    ai_response_generated=False
                )
                
                session.add(message)
                session.commit()
                session.refresh(message)

                # Expunge the message from the session to avoid binding issues
                session.expunge(message)

                logger.debug(f"Message stored: {message.id}")
                return message
                
        except Exception as e:
            logger.error(f"Error storing message: {e}")
            return None
    
    async def trigger_ai_processing(self, message: Message, contact_id: int, conversation: Optional[Conversation]):
        """Trigger AI processing for the message."""
        try:
            # Get contact and message in a fresh session to avoid session binding issues
            with self.db_manager.get_session() as session:
                # Refetch contact to ensure it's bound to this session
                contact = session.query(Contact).filter_by(id=contact_id).first()
                if not contact:
                    logger.warning(f"Contact {contact_id} not found for AI processing")
                    return

                # Only process if contact has given consent or is in whitelist
                if not contact.consent_given and contact.category not in ['family', 'friends']:
                    logger.debug(f"Skipping AI processing for {contact.phone_number} - no consent")
                    return

                # Refetch message to ensure it's bound to this session
                db_message = session.query(Message).filter_by(id=message.id).first()
                if not db_message:
                    logger.warning(f"Message {message.id} not found for AI processing")
                    return

                # Mark message as being processed
                db_message.processed = True
                session.commit()

                logger.info(f"Triggering AI processing for message from {contact.phone_number}")

                # TODO: Integrate with AI engine
                # This will be implemented when we create the AI core engine
                # For now, just log that we would process it
                logger.debug(f"AI processing would be triggered for message: {db_message.message_text[:50]}...")

        except Exception as e:
            logger.error(f"Error triggering AI processing: {e}")
    
    async def handle_message_ack(self, ack_data: Dict[str, Any]):
        """Handle message acknowledgment events."""
        try:
            message_id = ack_data.get('id')
            ack_status = ack_data.get('ack')
            
            # ACK statuses:
            # 0: Message sent
            # 1: Message delivered to server
            # 2: Message delivered to recipient
            # 3: Message read by recipient
            
            logger.debug(f"Message {message_id} acknowledgment: {ack_status}")
            
            # TODO: Update message status in database
            # This could be useful for tracking delivery and read receipts
            
        except Exception as e:
            logger.error(f"Error handling message acknowledgment: {e}")
    
    async def handle_group_join(self, join_data: Dict[str, Any]):
        """Handle group join events."""
        try:
            chat_id = join_data.get('chatId')
            who = join_data.get('who')
            timestamp = join_data.get('timestamp')
            
            logger.info(f"User {who} joined group {chat_id}")
            
            # TODO: Process group membership changes
            # This could be useful for group management features
            
        except Exception as e:
            logger.error(f"Error handling group join: {e}")
    
    async def handle_group_leave(self, leave_data: Dict[str, Any]):
        """Handle group leave events."""
        try:
            chat_id = leave_data.get('chatId')
            who = leave_data.get('who')
            timestamp = leave_data.get('timestamp')
            
            logger.info(f"User {who} left group {chat_id}")
            
            # TODO: Process group membership changes
            
        except Exception as e:
            logger.error(f"Error handling group leave: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get message processing statistics."""
        try:
            with self.db_manager.get_session() as session:
                total_messages = session.query(Message).count()
                processed_messages = session.query(Message).filter_by(processed=True).count()
                ai_responses = session.query(Message).filter_by(ai_response_generated=True).count()
                
                return {
                    'total_messages': total_messages,
                    'processed_messages': processed_messages,
                    'ai_responses_generated': ai_responses,
                    'processing_rate': processed_messages / total_messages if total_messages > 0 else 0
                }
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return {}
